<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>为你画一颗心 💕</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            height: 100vh;
            background: linear-gradient(135deg, 
                #ffeef8 0%, 
                #ffe0f0 25%, 
                #ffd1e8 50%, 
                #ffb3d9 75%, 
                #ff9dd1 100%);
            overflow: hidden;
            font-family: 'Dancing Script', cursive;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        /* 浪漫粒子背景 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 182, 193, 0.8);
            border-radius: 50%;
            animation: float 6s infinite ease-in-out;
        }
        
        .particle:nth-child(2n) {
            background: rgba(255, 105, 180, 0.6);
            animation-delay: -2s;
        }
        
        .particle:nth-child(3n) {
            background: rgba(255, 20, 147, 0.4);
            animation-delay: -4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
        }
        
        /* 主容器 */
        .container {
            text-align: center;
            z-index: 10;
            position: relative;
        }
        
        .title {
            font-size: 3rem;
            color: #d63384;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            animation: titleGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes titleGlow {
            from {
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            }
            to {
                text-shadow: 2px 2px 20px rgba(214, 51, 132, 0.5);
            }
        }
        
        /* SVG 容器 */
        .heart-container {
            position: relative;
            display: inline-block;
        }
        
        .heart-svg {
            filter: drop-shadow(0 0 20px rgba(255, 105, 180, 0.5));
        }
        
        /* 爱心路径动画 */
        .heart-path {
            fill: none;
            stroke: url(#heartGradient);
            stroke-width: 3;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawHeart 4s ease-in-out forwards;
        }
        
        @keyframes drawHeart {
            to {
                stroke-dashoffset: 0;
            }
        }
        
        /* 爱心填充动画 */
        .heart-fill {
            fill: url(#heartFillGradient);
            opacity: 0;
            animation: fillHeart 2s ease-in-out 4s forwards;
        }
        
        @keyframes fillHeart {
            to {
                opacity: 1;
            }
        }
        
        /* 光环效果 */
        .glow-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            border: 2px solid rgba(255, 105, 180, 0.3);
            border-radius: 50%;
            animation: pulse 3s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.3;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0.1;
            }
        }
        
        /* 底部文字 */
        .message {
            margin-top: 2rem;
            font-size: 1.5rem;
            color: #d63384;
            opacity: 0;
            animation: fadeInMessage 1s ease-in-out 6s forwards;
        }
        
        @keyframes fadeInMessage {
            to {
                opacity: 1;
            }
        }
        
        /* 重播按钮 */
        .replay-btn {
            margin-top: 1rem;
            padding: 10px 20px;
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: white;
            border: none;
            border-radius: 25px;
            font-family: 'Dancing Script', cursive;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            animation: fadeInButton 1s ease-in-out 7s forwards;
            transition: all 0.3s ease;
        }
        
        .replay-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }
        
        @keyframes fadeInButton {
            to {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>
    
    <div class="container">
        <h1 class="title">为你画一颗心</h1>
        
        <div class="heart-container">
            <div class="glow-ring"></div>
            <svg class="heart-svg" width="300" height="270" viewBox="0 0 300 270">
                <defs>
                    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ff69b4;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#ff1493;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#dc143c;stop-opacity:1" />
                    </linearGradient>
                    <radialGradient id="heartFillGradient" cx="50%" cy="40%">
                        <stop offset="0%" style="stop-color:#ffb6c1;stop-opacity:0.8" />
                        <stop offset="70%" style="stop-color:#ff69b4;stop-opacity:0.9" />
                        <stop offset="100%" style="stop-color:#ff1493;stop-opacity:1" />
                    </radialGradient>
                </defs>
                
                <!-- 爱心填充 -->
                <path class="heart-fill" d="M150,250 C150,250 50,180 50,120 C50,80 80,50 120,50 C135,50 150,60 150,60 C150,60 165,50 180,50 C220,50 250,80 250,120 C250,180 150,250 150,250 Z"/>
                
                <!-- 爱心轮廓 -->
                <path class="heart-path" d="M150,250 C150,250 50,180 50,120 C50,80 80,50 120,50 C135,50 150,60 150,60 C150,60 165,50 180,50 C220,50 250,80 250,120 C250,180 150,250 150,250 Z"/>
            </svg>
        </div>
        
        <p class="message">愿这颗心承载我所有的爱意 💕</p>
        <button class="replay-btn" onclick="replayAnimation()">重新播放</button>
    </div>

    <script>
        // 创建粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        
        // 重播动画
        function replayAnimation() {
            const heartPath = document.querySelector('.heart-path');
            const heartFill = document.querySelector('.heart-fill');
            const message = document.querySelector('.message');
            const button = document.querySelector('.replay-btn');
            
            // 重置动画
            heartPath.style.animation = 'none';
            heartFill.style.animation = 'none';
            message.style.animation = 'none';
            button.style.animation = 'none';
            
            // 强制重绘
            heartPath.offsetHeight;
            
            // 重新开始动画
            setTimeout(() => {
                heartPath.style.animation = 'drawHeart 4s ease-in-out forwards';
                heartFill.style.animation = 'fillHeart 2s ease-in-out 4s forwards';
                message.style.animation = 'fadeInMessage 1s ease-in-out 6s forwards';
                button.style.animation = 'fadeInButton 1s ease-in-out 7s forwards';
            }, 10);
        }
        
        // 页面加载完成后创建粒子
        window.addEventListener('load', createParticles);
    </script>
</body>
</html>
